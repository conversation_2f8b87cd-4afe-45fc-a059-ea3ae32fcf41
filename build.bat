@echo off
setlocal enabledelayedexpansion

REM Windows Batch build script for labitbu
echo === Labitbu Windows Build Script ===

REM 定义变量
set DEST_DIR=docs
set PKG_NAME=labitbu
set PKG_DIR=%DEST_DIR%\pkg

echo 检查必要工具...

REM 检查 Rust 是否安装
rustc --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Rust。请先安装 Rust:
    echo https://rustup.rs/
    pause
    exit /b 1
)

REM 检查 wasm-pack 是否安装
wasm-pack --version >nul 2>&1
if errorlevel 1 (
    echo 未找到 wasm-pack，正在安装...
    cargo install wasm-pack
    if errorlevel 1 (
        echo wasm-pack 安装失败
        pause
        exit /b 1
    )
)

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到 Node.js，将跳过测试步骤
    set SKIP_TESTS=1
) else (
    set SKIP_TESTS=0
)

echo 工具检查完成

REM 运行测试（如果 Node.js 可用）
if !SKIP_TESTS! == 0 (
    echo 运行 wasm-bindgen 测试...
    wasm-pack test --node
    if errorlevel 1 (
        echo 测试失败
        pause
        exit /b 1
    )
    echo 测试通过
) else (
    echo 跳过测试步骤
)

REM 清理旧的构建文件
echo 清理旧的构建文件...
if exist "%PKG_DIR%" (
    rmdir /s /q "%PKG_DIR%"
    echo 已删除旧的 %PKG_DIR% 目录
)

REM 构建 WebAssembly 包
echo 使用 wasm-pack 构建发布版本...
wasm-pack build --release --target web --out-dir %PKG_DIR% --out-name %PKG_NAME%

if errorlevel 1 (
    echo wasm-pack 构建失败
    pause
    exit /b 1
)

echo WebAssembly 构建完成

REM 清理不需要的文件
echo 清理不需要的文件...
if exist "%PKG_DIR%\.gitignore" (
    del "%PKG_DIR%\.gitignore"
    echo 已删除 .gitignore
)

if exist "%PKG_DIR%\README.md" (
    del "%PKG_DIR%\README.md"
    echo 已删除 README.md
)

REM 复制必要文件到 docs 目录
echo 复制文件到 docs 目录...

REM 确保 docs 目录存在
if not exist "%DEST_DIR%" (
    mkdir "%DEST_DIR%"
    echo 创建了 %DEST_DIR% 目录
)

REM 复制 index.html
if exist "index.html" (
    copy /y "index.html" "%DEST_DIR%\" >nul
    echo 已复制 index.html
) else (
    echo 警告: 未找到 index.html
)

REM 复制 labitbu-traits.json
if exist "labitbu-traits.json" (
    copy /y "labitbu-traits.json" "%DEST_DIR%\" >nul
    echo 已复制 labitbu-traits.json
) else (
    echo 警告: 未找到 labitbu-traits.json
)

REM 显示构建结果
echo 显示构建结果...
if exist "%PKG_DIR%\%PKG_NAME%_bg.wasm" (
    echo ✓ %PKG_DIR%\%PKG_NAME%_bg.wasm
) else (
    echo ✗ 未找到 %PKG_DIR%\%PKG_NAME%_bg.wasm
)

if exist "%PKG_DIR%\%PKG_NAME%.js" (
    echo ✓ %PKG_DIR%\%PKG_NAME%.js
) else (
    echo ✗ 未找到 %PKG_DIR%\%PKG_NAME%.js
)

echo.
echo === 构建完成! ===
echo 要启动本地服务器，请运行:
echo python -m http.server --directory docs 8080
echo 或者:
echo npx serve docs -p 8080
echo.
echo 然后在浏览器中访问: http://localhost:8080
echo.
pause
