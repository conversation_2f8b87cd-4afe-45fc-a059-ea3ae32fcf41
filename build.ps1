# Windows PowerShell build script for labitbu
# 设置错误时停止执行
$ErrorActionPreference = "Stop"

# 定义变量
$DEST_DIR = "docs"
$PKG_NAME = "labitbu"
$PKG_DIR = "$DEST_DIR\pkg"

Write-Host "=== Labitbu Windows Build Script ===" -ForegroundColor Green

# 检查必要的工具是否安装
Write-Host "检查必要工具..." -ForegroundColor Yellow

# 检查 Rust 是否安装
if (!(Get-Command "rustc" -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到 Rust。请先安装 Rust:" -ForegroundColor Red
    Write-Host "https://rustup.rs/" -ForegroundColor Red
    exit 1
}

# 检查 wasm-pack 是否安装
if (!(Get-Command "wasm-pack" -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到 wasm-pack。正在安装..." -ForegroundColor Yellow
    cargo install wasm-pack
    if ($LASTEXITCODE -ne 0) {
        Write-Host "wasm-pack 安装失败" -ForegroundColor Red
        exit 1
    }
}

# 检查 Node.js 是否安装（用于测试）
if (!(Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Host "警告: 未找到 Node.js，将跳过测试步骤" -ForegroundColor Yellow
    $SKIP_TESTS = $true
} else {
    $SKIP_TESTS = $false
}

Write-Host "工具检查完成" -ForegroundColor Green

# 运行测试（如果 Node.js 可用）
if (-not $SKIP_TESTS) {
    Write-Host "运行 wasm-bindgen 测试..." -ForegroundColor Yellow
    wasm-pack test --node
    if ($LASTEXITCODE -ne 0) {
        Write-Host "测试失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "测试通过" -ForegroundColor Green
} else {
    Write-Host "跳过测试步骤" -ForegroundColor Yellow
}

# 清理旧的构建文件
Write-Host "清理旧的构建文件..." -ForegroundColor Yellow
if (Test-Path $PKG_DIR) {
    Remove-Item -Recurse -Force $PKG_DIR
    Write-Host "已删除旧的 $PKG_DIR 目录" -ForegroundColor Green
}

# 构建 WebAssembly 包
Write-Host "使用 wasm-pack 构建发布版本..." -ForegroundColor Yellow
wasm-pack build --release --target web --out-dir $PKG_DIR --out-name $PKG_NAME

if ($LASTEXITCODE -ne 0) {
    Write-Host "wasm-pack 构建失败" -ForegroundColor Red
    exit 1
}

Write-Host "WebAssembly 构建完成" -ForegroundColor Green

# 清理不需要的文件
Write-Host "清理不需要的文件..." -ForegroundColor Yellow
$gitignorePath = "$PKG_DIR\.gitignore"
$readmePath = "$PKG_DIR\README.md"

if (Test-Path $gitignorePath) {
    Remove-Item $gitignorePath
    Write-Host "已删除 .gitignore" -ForegroundColor Green
}

if (Test-Path $readmePath) {
    Remove-Item $readmePath
    Write-Host "已删除 README.md" -ForegroundColor Green
}

# 复制必要文件到 docs 目录
Write-Host "复制文件到 docs 目录..." -ForegroundColor Yellow

# 确保 docs 目录存在
if (!(Test-Path $DEST_DIR)) {
    New-Item -ItemType Directory -Path $DEST_DIR
    Write-Host "创建了 $DEST_DIR 目录" -ForegroundColor Green
}

# 复制 index.html
if (Test-Path "index.html") {
    Copy-Item "index.html" "$DEST_DIR\" -Force
    Write-Host "已复制 index.html" -ForegroundColor Green
} else {
    Write-Host "警告: 未找到 index.html" -ForegroundColor Yellow
}

# 复制 labitbu-traits.json
if (Test-Path "labitbu-traits.json") {
    Copy-Item "labitbu-traits.json" "$DEST_DIR\" -Force
    Write-Host "已复制 labitbu-traits.json" -ForegroundColor Green
} else {
    Write-Host "警告: 未找到 labitbu-traits.json" -ForegroundColor Yellow
}

# 显示构建结果
Write-Host "显示构建结果..." -ForegroundColor Yellow
$wasmFile = "$PKG_DIR\${PKG_NAME}_bg.wasm"
$jsFile = "$PKG_DIR\${PKG_NAME}.js"

if (Test-Path $wasmFile) {
    $wasmSize = (Get-Item $wasmFile).Length
    Write-Host "✓ $wasmFile ($('{0:N0}' -f $wasmSize) bytes)" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到 $wasmFile" -ForegroundColor Red
}

if (Test-Path $jsFile) {
    $jsSize = (Get-Item $jsFile).Length
    Write-Host "✓ $jsFile ($('{0:N0}' -f $jsSize) bytes)" -ForegroundColor Green
} else {
    Write-Host "✗ 未找到 $jsFile" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 构建完成! ===" -ForegroundColor Green
Write-Host "要启动本地服务器，请运行:" -ForegroundColor Cyan
Write-Host "python -m http.server --directory docs 8080" -ForegroundColor White
Write-Host "或者:" -ForegroundColor Cyan
Write-Host "npx serve docs -p 8080" -ForegroundColor White
Write-Host ""
Write-Host "然后在浏览器中访问: http://localhost:8080" -ForegroundColor Cyan
