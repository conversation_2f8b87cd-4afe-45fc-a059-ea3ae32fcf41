# Windows PowerShell build script for labitbu
# Set error action preference to stop on errors
$ErrorActionPreference = "Stop"

# Define variables
$DEST_DIR = "docs"
$PKG_NAME = "labitbu"
$PKG_DIR = "$DEST_DIR\pkg"

Write-Host "=== Labitbu Windows Build Script ===" -ForegroundColor Green

# Check required tools
Write-Host "Checking required tools..." -ForegroundColor Yellow

# Check if Rust is installed
if (!(Get-Command "rustc" -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: Rust not found. Please install Rust first:" -ForegroundColor Red
    Write-Host "https://rustup.rs/" -ForegroundColor Red
    exit 1
}

# Check if wasm-pack is installed
if (!(Get-Command "wasm-pack" -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: wasm-pack not found. Installing..." -ForegroundColor Yellow
    cargo install wasm-pack
    if ($LASTEXITCODE -ne 0) {
        Write-Host "wasm-pack installation failed" -ForegroundColor Red
        exit 1
    }
}

# Check if Node.js is installed (for testing)
if (!(Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Host "WARNING: Node.js not found, skipping tests" -ForegroundColor Yellow
    $SKIP_TESTS = $true
} else {
    $SKIP_TESTS = $false
}

Write-Host "Tool check completed" -ForegroundColor Green

# Run tests (if Node.js is available)
if (-not $SKIP_TESTS) {
    Write-Host "Running wasm-bindgen tests..." -ForegroundColor Yellow
    wasm-pack test --node
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Tests failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "Tests passed" -ForegroundColor Green
} else {
    Write-Host "Skipping test step" -ForegroundColor Yellow
}

# Clean old build files
Write-Host "Cleaning old build files..." -ForegroundColor Yellow
if (Test-Path $PKG_DIR) {
    Remove-Item -Recurse -Force $PKG_DIR
    Write-Host "Removed old $PKG_DIR directory" -ForegroundColor Green
}

# Build WebAssembly package
Write-Host "Building release package with wasm-pack..." -ForegroundColor Yellow
wasm-pack build --release --target web --out-dir $PKG_DIR --out-name $PKG_NAME

if ($LASTEXITCODE -ne 0) {
    Write-Host "wasm-pack build failed" -ForegroundColor Red
    exit 1
}

Write-Host "WebAssembly build completed" -ForegroundColor Green

# Clean unnecessary files
Write-Host "Cleaning unnecessary files..." -ForegroundColor Yellow
$gitignorePath = "$PKG_DIR\.gitignore"
$readmePath = "$PKG_DIR\README.md"

if (Test-Path $gitignorePath) {
    Remove-Item $gitignorePath
    Write-Host "Removed .gitignore" -ForegroundColor Green
}

if (Test-Path $readmePath) {
    Remove-Item $readmePath
    Write-Host "Removed README.md" -ForegroundColor Green
}

# Copy necessary files to docs directory
Write-Host "Copying files to docs directory..." -ForegroundColor Yellow

# Ensure docs directory exists
if (!(Test-Path $DEST_DIR)) {
    New-Item -ItemType Directory -Path $DEST_DIR
    Write-Host "Created $DEST_DIR directory" -ForegroundColor Green
}

# Copy index.html
if (Test-Path "index.html") {
    Copy-Item "index.html" "$DEST_DIR\" -Force
    Write-Host "Copied index.html" -ForegroundColor Green
} else {
    Write-Host "WARNING: index.html not found" -ForegroundColor Yellow
}

# Copy labitbu-traits.json
if (Test-Path "labitbu-traits.json") {
    Copy-Item "labitbu-traits.json" "$DEST_DIR\" -Force
    Write-Host "Copied labitbu-traits.json" -ForegroundColor Green
} else {
    Write-Host "WARNING: labitbu-traits.json not found" -ForegroundColor Yellow
}

# Display build results
Write-Host "Displaying build results..." -ForegroundColor Yellow
$wasmFile = "$PKG_DIR\${PKG_NAME}_bg.wasm"
$jsFile = "$PKG_DIR\${PKG_NAME}.js"

if (Test-Path $wasmFile) {
    $wasmSize = (Get-Item $wasmFile).Length
    $wasmSizeFormatted = "{0:N0}" -f $wasmSize
    Write-Host "OK $wasmFile ($wasmSizeFormatted bytes)" -ForegroundColor Green
} else {
    Write-Host "ERROR: $wasmFile not found" -ForegroundColor Red
}

if (Test-Path $jsFile) {
    $jsSize = (Get-Item $jsFile).Length
    $jsSizeFormatted = "{0:N0}" -f $jsSize
    Write-Host "OK $jsFile ($jsSizeFormatted bytes)" -ForegroundColor Green
} else {
    Write-Host "ERROR: $jsFile not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Build Complete! ===" -ForegroundColor Green
Write-Host "To start local server, run:" -ForegroundColor Cyan
Write-Host "python -m http.server --directory docs 8080" -ForegroundColor White
Write-Host "or:" -ForegroundColor Cyan
Write-Host "npx serve docs -p 8080" -ForegroundColor White
Write-Host ""
Write-Host "Then visit: http://localhost:8080" -ForegroundColor Cyan
