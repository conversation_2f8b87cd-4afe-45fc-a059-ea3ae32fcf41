@echo off
setlocal enabledelayedexpansion

REM Windows Batch build script for labitbu
echo === Labitbu Windows Build Script ===

REM Define variables
set DEST_DIR=docs
set PKG_NAME=labitbu
set PKG_DIR=%DEST_DIR%\pkg

echo Checking required tools...

REM Check if Rust is installed
rustc --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Rust not found. Please install Rust first:
    echo https://rustup.rs/
    pause
    exit /b 1
)

REM Check if wasm-pack is installed
wasm-pack --version >nul 2>&1
if errorlevel 1 (
    echo wasm-pack not found, installing...
    cargo install wasm-pack
    if errorlevel 1 (
        echo wasm-pack installation failed
        pause
        exit /b 1
    )
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Node.js not found, skipping tests
    set SKIP_TESTS=1
) else (
    set SKIP_TESTS=0
)

echo Tool check completed

REM Run tests (if Node.js is available)
if !SKIP_TESTS! == 0 (
    echo Running wasm-bindgen tests...
    wasm-pack test --node
    if errorlevel 1 (
        echo Tests failed
        pause
        exit /b 1
    )
    echo Tests passed
) else (
    echo Skipping test step
)

REM Clean old build files
echo Cleaning old build files...
if exist "%PKG_DIR%" (
    rmdir /s /q "%PKG_DIR%"
    echo Removed old %PKG_DIR% directory
)

REM Build WebAssembly package
echo Building release package with wasm-pack...
wasm-pack build --release --target web --out-dir %PKG_DIR% --out-name %PKG_NAME%

if errorlevel 1 (
    echo wasm-pack build failed
    pause
    exit /b 1
)

echo WebAssembly build completed

REM Clean unnecessary files
echo Cleaning unnecessary files...
if exist "%PKG_DIR%\.gitignore" (
    del "%PKG_DIR%\.gitignore"
    echo Removed .gitignore
)

if exist "%PKG_DIR%\README.md" (
    del "%PKG_DIR%\README.md"
    echo Removed README.md
)

REM Copy necessary files to docs directory
echo Copying files to docs directory...

REM Ensure docs directory exists
if not exist "%DEST_DIR%" (
    mkdir "%DEST_DIR%"
    echo Created %DEST_DIR% directory
)

REM Copy index.html
if exist "index.html" (
    copy /y "index.html" "%DEST_DIR%\" >nul
    echo Copied index.html
) else (
    echo WARNING: index.html not found
)

REM Copy labitbu-traits.json
if exist "labitbu-traits.json" (
    copy /y "labitbu-traits.json" "%DEST_DIR%\" >nul
    echo Copied labitbu-traits.json
) else (
    echo WARNING: labitbu-traits.json not found
)

REM Display build results
echo Displaying build results...
if exist "%PKG_DIR%\%PKG_NAME%_bg.wasm" (
    echo OK %PKG_DIR%\%PKG_NAME%_bg.wasm
) else (
    echo ERROR: %PKG_DIR%\%PKG_NAME%_bg.wasm not found
)

if exist "%PKG_DIR%\%PKG_NAME%.js" (
    echo OK %PKG_DIR%\%PKG_NAME%.js
) else (
    echo ERROR: %PKG_DIR%\%PKG_NAME%.js not found
)

echo.
echo === Build Complete! ===
echo To start local server, run:
echo python -m http.server --directory docs 8080
echo or:
echo npx serve docs -p 8080
echo.
echo Then visit: http://localhost:8080
echo.
pause
